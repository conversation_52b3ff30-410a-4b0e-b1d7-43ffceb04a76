<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".SousActivity">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="44dp"
        android:layout_marginRight="8dp"
        android:text="@string/sous_activit"
        android:textSize="32sp"
        app:layout_constraintHorizontal_bias="0.502"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="44dp"
        android:layout_marginRight="8dp"
        android:autofillHints="@string/message"
        android:ems="10"
        android:hint="@string/message"
        android:inputType="text"
        app:layout_constraintHorizontal_bias="0.503"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <Button
        android:id="@+id/bt_finir"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="78dp"
        android:layout_marginRight="8dp"
        android:text="@string/finir"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_message" />

    <Button
        android:id="@+id/bt_share"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="59dp"
        android:text="Share"
        app:layout_constraintEnd_toEndOf="@+id/bt_finir"
        app:layout_constraintStart_toStartOf="@+id/bt_finir"
        app:layout_constraintTop_toBottomOf="@+id/bt_finir" />

</androidx.constraintlayout.widget.ConstraintLayout>