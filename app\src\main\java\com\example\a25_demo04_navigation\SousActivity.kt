package com.example.a25_demo04_navigation

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

class SousActivity : AppCompatActivity() {

    private lateinit var etMessage: EditText
    private lateinit var tvTitle: TextView
    private lateinit var btFinir: Button
    private lateinit var btShare: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_sous)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        etMessage = findViewById(R.id.et_message)
        btFinir = findViewById(R.id.bt_finir)
        btShare = findViewById(R.id.bt_share)
        tvTitle = findViewById(R.id.tv_title)

        // clickListener anonyme : renvoie un résultat à l'activité appelante
        btFinir.setOnClickListener(View.OnClickListener {
            val message = etMessage.text.toString()
            // nouvelle intention pour retour
            val intent = intent
            // passage de valeur - noter qu'ici la fonction d'affectation n'est pas typée contrairement à celle de récupération
            intent.putExtra(MESSAGE, message)
            // Active la navigation retour vers l'appelant
            setResult(RESULT_OK, intent)
            finish()
        })
        btShare.setOnClickListener(View.OnClickListener { shareMessage() })
    }

    // Action_SEND : Cherchera un client courriel dans la liste des app installées
    private fun shareMessage() {
        val message = etMessage.text.toString()
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = "text/plain"
        intent.putExtra(Intent.EXTRA_SUBJECT, "Mon message")
        intent.putExtra(Intent.EXTRA_TEXT, message)
        startActivity(intent)
    }

    // valeur statique associés à la classe : nécessite un companion object en Kotlin
    companion object {
        const val MESSAGE = "MESSAGE"
    }

}