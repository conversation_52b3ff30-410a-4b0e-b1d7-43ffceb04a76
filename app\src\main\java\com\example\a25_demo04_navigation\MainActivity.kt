package com.example.a25_demo04_navigation

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.ContactsContract
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

class MainActivity : AppCompatActivity(), View.OnClickListener {

    private lateinit var btSecondeActivity: Button
    private lateinit var btSousActivity: Button
    private lateinit var btWeb: Button
    private lateinit var btContact: Button
    private lateinit var btMap: Button
    private lateinit var tvResultat: TextView

    // Déclaration d'un objet Launcher pour navigation vers activité avec résultats
    var activiteResultat: ActivityResultLauncher<Intent>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // instanciation des widgets
        btSecondeActivity = findViewById(R.id.bt_secondeactivity)
        btSousActivity = findViewById(R.id.bt_sousactivity)
        btWeb = findViewById(R.id.bt_web)
        btContact = findViewById(R.id.bt_contact)
        btMap = findViewById(R.id.bt_map)
        tvResultat = findViewById(R.id.tv_message)

        // Passage de gestion de clics aux boutons
        btSecondeActivity.setOnClickListener(this)
        btSousActivity.setOnClickListener(this)
        btWeb.setOnClickListener(this)
        btContact.setOnClickListener(this)
        btMap.setOnClickListener(this)

        // Callback pour navigation vers SousActivity qui renverra un résultat
        // Voir lancement l89
        // instanciation du launcher :
        //      classe anonyme ActivityResultContracts + méthode de rappel pour traiter résultat
        activiteResultat = registerForActivityResult<Intent, ActivityResult>(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == RESULT_OK) {
                // Récupération des données reçues : contenues dans l'intent renvoyé
                val data = result.data
                // la fonction de récupération est typée
                val message = data!!.getStringExtra(SousActivity.MESSAGE)
                tvResultat.setText(message)
            } else {
                tvResultat.setText(R.string.erreur)
            }
        }
    }

    // Gestionnaire de clics sur les différents boutons du layout
    override fun onClick(v: View) {
        // déclaration d'Intent pour la navigation
        val intent: Intent
        when (v.id) {
            // Navigation vers SecondeActivity
            R.id.bt_secondeactivity -> {
                intent = Intent(this@MainActivity, SecondeActivity::class.java)
                startActivity(intent)
            }
            // Navigation vers Sous Activity : renverra un résultat
            // Voir code de SousActivity pour le renvoi du résultat
            R.id.bt_sousactivity -> {
                intent = Intent(applicationContext, SousActivity::class.java)
                // Méthode de classe Launcher
                activiteResultat!!.launch(intent)
            }

            R.id.bt_web -> {
                intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://www.cegepgarneau.ca"))
                startActivity(Intent.createChooser(intent, "Choose Application"))
            }
            R.id.bt_contact -> {
                intent = Intent(Intent.ACTION_PICK, ContactsContract.Contacts.CONTENT_URI)
                startActivity(Intent.createChooser(intent, "Choose Application"))
            }
            R.id.bt_map -> {
                intent = Intent(Intent.ACTION_VIEW, Uri.parse("geo:46.793531,-71.262906"))
                startActivity(Intent.createChooser(intent, "Choose Application"))
            }
        }
    }
}