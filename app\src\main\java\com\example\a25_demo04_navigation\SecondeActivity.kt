package com.example.a25_demo04_navigation

import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

class SecondeActivity : AppCompatActivity() {

    private lateinit var btFinir: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_seconde)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        btFinir = findViewById(R.id.bt_finir)
        btFinir.setOnClickListener(View.OnClickListener {
            Toast.makeText(applicationContext, "Activité terminée", Toast.LENGTH_SHORT).show()
            finish()
        })
    }
}